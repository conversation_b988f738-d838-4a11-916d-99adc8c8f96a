<footer class="footer">
        <div class="columnfooterlog">
          <div class="rowfooterlogo">
            <img src="../public/images/img_footer_logo.svg" alt="Footerlogo" class="footerlogo_one" />
            <div class="rowcontact_two">
              <div class="rownavigation">
                <div class="columnnavigatio">
                  <h5 class="copyright2024 ui heading size-headingxs">Navigation</h5>
                  <ul class="columnhome_one">
                    <li>
                      <a href="/" target="_blank" rel="noreferrer">
                        <p class="description-20 ui text size-texts">Home</p>
                      </a>
                    </li>
                    <li>
                      <a href="https://safecarhauler.com/how-to-ship-a-car">
                        <p class="description-20 ui text size-texts">How it Works</p>
                      </a>
                    </li>
                    <li>
                      <a href="blog.php" target="_blank" rel="noreferrer">
                        <p class="description-20 ui text size-texts">Blog</p>
                      </a>
                    </li>
                    <li>
                      <a href="contact.php" target="_blank" rel="noreferrer">
                        <p class="description-20 ui text size-texts">Contact</p>
                      </a>
                    </li>
                  </ul>
                </div>
                <div class="columnservices">
                  <h5 class="copyright2024 ui heading size-headingxs">Services</h5>
                  <ul class="columnhome_one">
                    <li>
                      <a href="https://safecarhauler.com/door-to-door-car-transport">
                        <p class="description-20 ui text size-texts">Door to Door Transport</p>
                      </a>
                    </li>
                    <li>
                      <a href="https://safecarhauler.com/exotic-car-transport">
                        <p class="description-20 ui text size-texts">Exotic Car Shipping</p>
                      </a>
                    </li>
                    <li>
                      <a href="https://safecarhauler.com/open-auto-transport">
                        <p class="description-20 ui text size-texts">Open Auto Transport</p>
                      </a>
                    </li>
                    <li>
                      <a href="https://safecarhauler.com/enclosed-auto-transport">
                        <p class="description-20 ui text size-texts">Enclosed Auto Transport</p>
                      </a>
                    </li>
                    <li>
                      <a href="https://safecarhauler.com/boat-shipping">
                        <p class="description-20 ui text size-texts">Boat Shipping</p>
                      </a>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="columnemail">
                <div class="columncontact">
                  <h5 class="copyright2024 ui heading size-headingxs">Contact</h5>
                  <a href="#" class="description-link">
                    <p class="description-20 ui text size-texts">
                      Dots Around Corp<br />7917 Bustelton Ave Philadelphia, PA 19152
                    </p>
                  </a>
                </div>
                <p class="email ui text size-texts"><EMAIL></p>
                <a href="tel:+18778788008"><p class="class-18778788008 ui text size-texts">(*************</p></a>
                <!--<div class="rowsocialicon">
                  <img src="../public/images/img_social_icon.svg" alt="Socialicon" class="socialicon_one" />
                  <img src="../public/images/img_social_icon_black_900.svg" alt="Socialicon" class="socialicon_one" />
                  <img src="../public/images/img_link.svg" alt="Link" class="socialicon_one" />
                  <img
                    src="../public/images/img_social_icon_black_900_24x24.svg"
                    alt="Socialicon"
                    class="socialicon_one"
                  />
                </div>-->
              </div>
            </div>
          </div>
          <div class="rowcopyright202" style="text-align: center;">
            <p class="copyright2024 ui text size-texts">Safe Car Hauler™ © 2025. Division of Dots Around Corp.</p>
            <p class="copyright2024 ui text size-texts">Fully Licensed & Bonded  |  U.S. DOT NO. 4127340  |  MC #157974</p>
            <div class="rowtermandcondi">
            <a href="https://www.iubenda.com/privacy-policy/28502363/cookie-policy" class="iubenda-nostyle iubenda-noiframe iubenda-embed iubenda-noiframe " title="Cookie Policy "><p class="copyright2024 ui text size-texts">Cookie Policy</p></a><script type="text/javascript">(function (w,d) {var loader = function () {var s = d.createElement("script"), tag = d.getElementsByTagName("script")[0]; s.src="https://cdn.iubenda.com/iubenda.js"; tag.parentNode.insertBefore(s,tag);}; if(w.addEventListener){w.addEventListener("load", loader, false);}else if(w.attachEvent){w.attachEvent("onload", loader);}else{w.onload = loader;}})(window, document);</script>
              <div class="lineone_one"></div>
            <a href="https://www.iubenda.com/terms-and-conditions/28502363" class="iubenda-nostyle iubenda-noiframe iubenda-embed iubenda-noiframe " title="Terms and Conditions "><p class="copyright2024 ui text size-texts">Terms and Conditions</p></a><script type="text/javascript">(function (w,d) {var loader = function () {var s = d.createElement("script"), tag = d.getElementsByTagName("script")[0]; s.src="https://cdn.iubenda.com/iubenda.js"; tag.parentNode.insertBefore(s,tag);}; if(w.addEventListener){w.addEventListener("load", loader, false);}else if(w.attachEvent){w.attachEvent("onload", loader);}else{w.onload = loader;}})(window, document);</script>
              <div class="lineone_one"></div>
                <a href="https://www.iubenda.com/privacy-policy/28502363" class="iubenda-nostyle iubenda-noiframe iubenda-embed iub-legal-only iubenda-noiframe " title="Privacy Policy "><p class="copyright2024 ui text size-texts">Privacy Policy</p></a><script type="text/javascript">(function (w,d) {var loader = function () {var s = d.createElement("script"), tag = d.getElementsByTagName("script")[0]; s.src="https://cdn.iubenda.com/iubenda.js"; tag.parentNode.insertBefore(s,tag);}; if(w.addEventListener){w.addEventListener("load", loader, false);}else if(w.attachEvent){w.attachEvent("onload", loader);}else{w.onload = loader;}})(window, document);</script>
            </div>
          </div>
        </div>
</footer>
<script defer src='../public/js/mob-menu.js'></script>
<script src="public/js/send.js"></script>
<script defer src='../public/js/countdown.js'></script>
<script>
const states = [
  "Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado", "Connecticut", "Delaware", "Florida", "Georgia",
  "Hawaii", "Idaho", "Illinois", "Indiana", "Iowa", "Kansas", "Kentucky", "Louisiana", "Maine", "Maryland", "Massachusetts",
  "Michigan", "Minnesota", "Mississippi", "Missouri", "Montana", "Nebraska", "Nevada", "New Hampshire", "New Jersey",
  "New Mexico", "New York", "North Carolina", "North Dakota", "Ohio", "Oklahoma", "Oregon", "Pennsylvania", "Rhode Island",
  "South Carolina", "South Dakota", "Tennessee", "Texas", "Utah", "Vermont", "Virginia", "Washington", "West Virginia",
  "Wisconsin", "Wyoming"
];

let currentPage = 0;
const statesPerPage = 10;
const totalPages = Math.ceil(states.length / statesPerPage);

function renderStates() {
  const statesGrid = document.getElementById("statesGrid");
  statesGrid.innerHTML = "";
  
  const start = currentPage * statesPerPage;
  const end = start + statesPerPage;
  const pageStates = states.slice(start, end);

  pageStates.forEach(state => {
    const button = document.createElement("button");
    button.className = "map-mob-f-state-button";
    button.textContent = state;
    statesGrid.appendChild(button);
  });

  const emptySlots = statesPerPage - pageStates.length;
  for (let i = 0; i < emptySlots; i++) {
    const emptyButton = document.createElement("button");
    emptyButton.className = "map-mob-f-state-button";
    emptyButton.style.visibility = "hidden";
    statesGrid.appendChild(emptyButton);
  }

  renderPagination();
}

function renderStates() {
  const statesGrid = document.getElementById("statesGrid");
  statesGrid.innerHTML = "";
  
  const start = currentPage * statesPerPage;
  const end = start + statesPerPage;
  const pageStates = states.slice(start, end);

  pageStates.forEach(state => {
    const button = document.createElement("button");
    button.className = "map-mob-f-state-button";
    button.textContent = state;
    
    button.onclick = () => {
      const url = `${state.toLowerCase().replace(/\s+/g, '-')}-car-shipping`;
      window.location.href = url;
    };
    
    statesGrid.appendChild(button);
  });

  const emptySlots = statesPerPage - pageStates.length;
  for (let i = 0; i < emptySlots; i++) {
    const emptyButton = document.createElement("button");
    emptyButton.className = "map-mob-f-state-button";
    emptyButton.style.visibility = "hidden";
    statesGrid.appendChild(emptyButton);
  }

  renderPagination();
}

function renderPagination() {
  const paginationDots = document.getElementById("paginationDots");
  paginationDots.innerHTML = "";

  for (let i = 0; i < totalPages; i++) {
    const dot = document.createElement("span");
    dot.className = "map-mob-f-dot" + (i === currentPage ? " map-mob-f-active" : "");
    dot.onclick = () => goToPage(i);
    paginationDots.appendChild(dot);
  }
}

function goToPage(page) {
  if (page >= 0 && page < totalPages) {
    currentPage = page;
    renderStates();
  }
}

function nextPage() {
  if (currentPage < totalPages - 1) {
    currentPage++;
  } else {
    currentPage = 0; 
  }
  renderStates();
}


function prevPage() {
  if (currentPage > 0) {
    currentPage--;
  } else {
    currentPage = totalPages - 1; 
  }
  renderStates();
}


document.addEventListener("DOMContentLoaded", renderStates);
</script>