
<style>
  .location-tooltip {
    position: absolute;
    top: -60px;
    left: 0;
    right: 0;
    background-color: #424242;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.4;
    opacity: 0;
    visibility: hidden;
    display: none;
    transition: all 0.3s ease-in-out;
    transform: translateY(10px);
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .location-tooltip.show {
    opacity: 1;
    visibility: visible;
    display: block;
    transform: translateY(0);
  }

  .tooltip-arrow {
    position: absolute;
    bottom: -8px;
    left: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #424242;
  }

  .input-wrapper.input-error {
    border: 1px solid #fb0000 !important;
  }

  .radio-error {
    border: 1px solid #fb0000 !important;
    border-radius: 10px;
    padding: 5px;
  }

  /* Enhanced mobile input handling */
  @media only screen and (max-width: 768px) {
    /* Smooth input focus transitions */
    input, select, textarea {
      transition: all 0.3s ease;
      -webkit-transition: all 0.3s ease;
    }

    input:focus, select:focus, textarea:focus {
      transform: translateZ(0);
      -webkit-transform: translateZ(0);
      outline: none;
      border-color: #CDA565;
      box-shadow: 0 0 0 2px rgba(205, 165, 101, 0.2);
    }

    /* Prevent zoom on input focus */
    input[type="text"],
    input[type="email"],
    input[type="tel"],
    input[type="number"],
    select,
    textarea {
      font-size: 16px !important; /* Prevents zoom on iOS */
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
    }

    /* Smooth scrolling for the entire page */
    html {
      scroll-behavior: smooth;
      -webkit-overflow-scrolling: touch;
    }

    .step {
      overflow-y: visible;
      -webkit-overflow-scrolling: touch;
      min-height: auto;
    }

    /* Ensure form container doesn't interfere with scrolling */
    .quote-1 {
      overflow: visible;
    }

    /* Input wrapper improvements */
    .input-wrapper {
      position: relative;
      /*margin-bottom: 1rem;*/
    }

    /* Ensure inputs are properly sized */
    .input-wrapper input {
      width: 100%;
      box-sizing: border-box;
      padding: 4px 13px;
      border-radius: 8px;
      /*border: 1px solid #ddd;*/
      background-color: #fff;
      color: #333;
    }
    .styled-select{
            padding: 13px !important;
        font-size: 14px !important;
        
    }
    .dropdown-icon{
            position: absolute!important;
    right: -30px !important;
    margin-top: 20px;
    transform: translateY(-50%)!important;
    /*width: 25px!important;*/
    height: 8px!important;
    }
    /* Enhanced focus states for better UX */
    .input-wrapper input:focus,
    .select-custom select:focus {
      border-color: #CDA565 !important;
      box-shadow: 0 0 0 3px rgba(205, 165, 101, 0.1) !important;
      background-color: #fff !important;
      transform: translateZ(0);
      -webkit-transform: translateZ(0);
    }

    /* Smooth transitions for all interactive elements */
    .input-wrapper,
    .select-custom,
    .step-buttom,
    .ui.checkbox {
      transition: all 0.2s ease;
      -webkit-transition: all 0.2s ease;
    }

    /* Prevent text selection issues on mobile */
    .input-wrapper input,
    .select-custom select {
      -webkit-user-select: text;
      -moz-user-select: text;
      -ms-user-select: text;
      user-select: text;
    }
  }
</style>

<div id="notification" class="notification" style="display: none;">Order Sent Successfully!</div>

<div class="row_three">
  <div>
    <div class="row_one">
      <div class="row container-xs">
        <div class="rowquicksecure">
          <div class="columnquicksecu">
            <h1 class="quicksecure ui heading size-heading2xl">
            Safest &amp; Most Reliable <br> Auto Transport Services
            </h1>
            <p class="experience ui text size-textmd" style="text-align: center !important;">The quickest and easiest method to ship your car nationwide</p>
            <button class="flex-row-center-center get_an_instant-2" onclick="window.location.href='quote.php'">Get an instant quote</button>
          </div>



<form id="transportForm" method="POST" action="send-leads" class="stackarrowdown">
  <div class="quote-1">
    <h2 class="getaninstant ui heading size-headings" style="margin-left: 20px;">
      <span>Get an instant Quote Now</span>
      <span class="getaninstant-span">or call <a href="tel:8778788008" class="getaninstant-span-call"> (*************</a></span>
    </h2>

    <!-- Step 1: From/To form -->
    <div class="step" id="step-1">
      <div class="input-group">
        <div class="input-wrapper" style="position: relative;">
          <!-- <div class="location-tooltip" id="tooltip-from">
            Begin typing a zip code or city and click a suggested location.
            <div class="tooltip-arrow"></div>
          </div> -->
          <img src="public/images/Vector.svg" alt="Location Icon" class="icon" />
          <input type="text" name="transport_from" id="transport_from" class="from-input" placeholder="From (ZIP or City)" required
                 autocomplete="address-line1" inputmode="text" />
        </div>
        <div class="input-wrapper" style="position: relative;">
          <!-- <div class="location-tooltip" id="tooltip-to">
            Begin typing a zip code or city and click a suggested location.
            <div class="tooltip-arrow"></div>
          </div> -->
          <img src="public/images/Vector-2.svg" alt="Location Icon" class="icon" />
          <input type="text" name="transport_to" id="transport_to" class="to-input" placeholder="To (ZIP or City)" required
                 autocomplete="address-line2" inputmode="text" />
        </div>
      </div>
      <label class="transoprt-type">Transport Type</label>
      <div class="rowtransporttyp">
        <label class="ui checkbox">
          <input type="radio" name="transport_type" value="Open" required />
          <div></div><span>Open</span>
        </label>
        <label class="ui checkbox">
          <input type="radio" name="transport_type" value="Enclosed" required />
          <div></div><span>Enclosed</span>
        </label>
      </div>

      <button type="button" class="step-buttom" onclick="nextStep(2)">Continue</button>
    </div>

    <!-- Step 2: Vehicle details -->
    <div class="step" id="step-2" style="display: none;">
      <div class="input-group">
        <div class="select-custom">
          <select name="vehicle_year" id="vehicle_year" class="styled-select" style="padding-right: 40px;" required>
            <option value="" disabled selected>Vehicle year</option>
            <!-- Populate dynamically with JavaScript if needed -->
          </select>
          <span class="dropdown-icon"></span>
        </div>
        <div class="select-custom">
          <select name="vehicle_brand" id="vehicle_brand" class="styled-select" style="padding-right: 40px;" required>
            <option value="" disabled selected>Vehicle Make</option>
            <!-- Populate dynamically -->
          </select>
          <span class="dropdown-icon"></span>
        </div>
        <div class="select-custom">
          <select name="vehicle_model" id="vehicle_model" class="styled-select" style="padding-right: 40px;" required>
            <option value="" disabled selected>Vehicle model</option>
            <!-- Populate dynamically -->
          </select>
          <span class="dropdown-icon"></span>
        </div>
      </div>
      <label class="vehicle-type">Is it operable?</label>
      <div class="rowtransporttyp">
        <label class="ui checkbox">
          <input type="radio" name="vehicle_operable" value="yes" required />
          <div></div><span>Yes</span>
        </label>
        <label class="ui checkbox">
          <input type="radio" name="vehicle_operable" value="no" required />
          <div></div><span>No</span>
        </label>
      </div>

      <button type="button" class="step-buttom" onclick="nextStep(3)">Continue</button>
    </div>

    <!-- Step 3: Contact details -->
    <div class="step" id="step-3" style="display: none;">
      <div class="input-group">
        <div class="input-wrapper">
          <input type="email" name="email" id="email" class="info-input" placeholder="Your Email" required
                 autocomplete="email" inputmode="email" />
        </div>
        <div class="input-wrapper">
          <input type="text" name="available_date" id="available_date" class="info-input" placeholder="Ship Date" readonly required
                 autocomplete="off" />
        </div>
        <div class="input-wrapper">
          <input type="text" name="name" id="name" class="info-input" placeholder="Your name" required
                 autocomplete="name" inputmode="text" />
        </div>
        <div class="input-wrapper">
          <input type="tel" name="phone" id="phone" class="info-input" placeholder="Your phone" required
                 autocomplete="tel" inputmode="tel" />
        </div>
      </div>
     <div class="privacy-section" style="    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    padding-left: 10px;
    padding-right: 10px;
    /* margin: 2rem auto 1.5rem; */
    align-items: center;
    ">
        <img style="width: 35px;
    /* height: 3.8rem; */
    margin-right: .5rem;
    margin-left: 10px;" src="public/images/SafeCarHauler-Shield.png" alt="SafeCarHauler Shield" class="privacy-shield">
        <p style="margin-bottom:0px; font-size:13px;  " class="privacy-text">We value your privacy and will not share <br> your personal information.</p>
      </div>
      <button type="submit" class="step-buttom" id="finishButton">Calculate Quote</button>
    </div>
  </div>
</form>

        </div>
      </div>
    </div>
  </div>
</div>
<style>
@media only screen and (max-width: 768px) {
  .privacy-section {
    /*flex-direction: column;*/
    align-items: flex-start;
    padding-left: 0;
    padding-right: 0;
  }
  .privacy-section>img{
    width:40px;
  }
  .privacy-shield {
    margin-right: 0;
    margin-left: 0;
    margin-bottom: 10px;
  }
  .privacy-text {
      width: 245px;
  }
  .burger-menu{
      width:25px !important;
  }
  .burger-menu > img{
      height: 20px !important;
        width: 100%;
  }
}

</style>

<script>
    document.getElementById('vehicle_year').addEventListener('change', function () {
    document.getElementById('vehicle_brand').disabled = false;
});
</script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js" data-cmp-ab="2"></script>
<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css" data-cmp-ab="2">
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js" data-cmp-ab="2"></script>
<script>

function initGoogleMaps() {
    console.log('Google Maps API loaded successfully');
    if (document.getElementById('transport_from')) {
        initializeAutocomplete(document.getElementById('transport_from'));
    }
    if (document.getElementById('transport_to')) {
        initializeAutocomplete(document.getElementById('transport_to'));
    }
}

// Enhanced mobile input handling and tooltip management
document.addEventListener('DOMContentLoaded', function() {
    const transportFrom = document.getElementById('transport_from');
    const transportTo = document.getElementById('transport_to');
    const tooltipFrom = document.getElementById('tooltip-from');
    const tooltipTo = document.getElementById('tooltip-to');

    // Mobile detection
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;

    // Add mobile-specific meta viewport if not present
    if (isMobile && !document.querySelector('meta[name="viewport"]')) {
        const viewport = document.createElement('meta');
        viewport.name = 'viewport';
        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
        document.head.appendChild(viewport);
    }

    // Enhanced mobile input focus handling
    function preventMobileScroll(inputElement) {
        if (window.innerWidth <= 768) { // Only on mobile devices
            let isInputFocused = false;
            let originalScrollY = 0;

            inputElement.addEventListener('focus', function(e) {
                if (isInputFocused) return; // Prevent multiple focus events

                isInputFocused = true;
                originalScrollY = window.scrollY;

                // Use requestAnimationFrame for smooth transitions
                requestAnimationFrame(() => {
                    // Scroll the input into view smoothly
                    const inputRect = this.getBoundingClientRect();
                    const viewportHeight = window.innerHeight;
                    const keyboardHeight = viewportHeight * 0.4; // Estimate keyboard height
                    const targetY = inputRect.top + window.scrollY - (viewportHeight - keyboardHeight) / 2;

                    // Smooth scroll to position
                    window.scrollTo({
                        top: Math.max(0, targetY),
                        behavior: 'smooth'
                    });

                    // Add a small delay to ensure smooth transition
                    setTimeout(() => {
                        // Ensure input stays visible
                        this.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                            inline: 'nearest'
                        });
                    }, 100);
                });
            });

            inputElement.addEventListener('blur', function() {
                if (!isInputFocused) return;

                isInputFocused = false;

                // Small delay to allow for smooth transition
                setTimeout(() => {
                    // Only restore scroll if user hasn't manually scrolled
                    const currentScrollY = window.scrollY;
                    const scrollDifference = Math.abs(currentScrollY - originalScrollY);

                    // If scroll difference is small, restore original position
                    if (scrollDifference < 100) {
                        window.scrollTo({
                            top: originalScrollY,
                            behavior: 'smooth'
                        });
                    }
                }, 150);
            });

            // Handle viewport changes (keyboard show/hide)
            let initialViewportHeight = window.innerHeight;

            window.addEventListener('resize', function() {
                if (isInputFocused) {
                    const currentViewportHeight = window.innerHeight;
                    const heightDifference = initialViewportHeight - currentViewportHeight;

                    // If viewport height decreased significantly (keyboard appeared)
                    if (heightDifference > 150) {
                        setTimeout(() => {
                            const focusedInput = document.activeElement;
                            if (focusedInput && focusedInput.tagName === 'INPUT') {
                                focusedInput.scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'center',
                                    inline: 'nearest'
                                });
                            }
                        }, 100);
                    }
                }
            });
        }
    }

    // Apply to all input fields with improved selector
    const allInputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="tel"], input[readonly]');
    allInputs.forEach(input => {
        preventMobileScroll(input);

        // Additional mobile-specific enhancements
        if (isMobile) {
            // Prevent double-tap zoom
            input.addEventListener('touchend', function(e) {
                e.preventDefault();
                this.focus();
            });

            // Handle input changes smoothly
            input.addEventListener('input', function() {
                // Debounce input changes to prevent excessive scrolling
                clearTimeout(this.inputTimeout);
                this.inputTimeout = setTimeout(() => {
                    if (document.activeElement === this) {
                        this.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                            inline: 'nearest'
                        });
                    }
                }, 100);
            });
        }
    });

    // Global mobile optimizations
    if (isMobile) {
        // Prevent body scroll when focusing on inputs
        let focusedInput = null;

        document.addEventListener('focusin', function(e) {
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'SELECT' || e.target.tagName === 'TEXTAREA') {
                focusedInput = e.target;
                document.body.style.overflow = 'hidden';
                document.body.style.position = 'fixed';
                document.body.style.width = '100%';
            }
        });

        document.addEventListener('focusout', function(e) {
            if (focusedInput) {
                setTimeout(() => {
                    if (!document.activeElement ||
                        (document.activeElement.tagName !== 'INPUT' &&
                         document.activeElement.tagName !== 'SELECT' &&
                         document.activeElement.tagName !== 'TEXTAREA')) {
                        document.body.style.overflow = '';
                        document.body.style.position = '';
                        document.body.style.width = '';
                        focusedInput = null;
                    }
                }, 100);
            }
        });

        // Handle orientation changes
        window.addEventListener('orientationchange', function() {
            setTimeout(() => {
                if (focusedInput) {
                    focusedInput.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center',
                        inline: 'nearest'
                    });
                }
            }, 500);
        });

        // Prevent zoom on double tap for form elements
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                if (event.target.tagName === 'INPUT' ||
                    event.target.tagName === 'SELECT' ||
                    event.target.tagName === 'TEXTAREA') {
                    event.preventDefault();
                }
            }
            lastTouchEnd = now;
        }, false);

        // Smooth keyboard handling
        let keyboardHeight = 0;
        const originalViewportHeight = window.innerHeight;

        window.addEventListener('resize', function() {
            const currentHeight = window.innerHeight;
            const heightDiff = originalViewportHeight - currentHeight;

            if (heightDiff > 150) { // Keyboard is likely open
                keyboardHeight = heightDiff;
                if (focusedInput) {
                    setTimeout(() => {
                        const rect = focusedInput.getBoundingClientRect();
                        const visibleHeight = currentHeight;
                        const inputBottom = rect.bottom;

                        if (inputBottom > visibleHeight - 50) { // Input is hidden by keyboard
                            const scrollAmount = inputBottom - visibleHeight + 100;
                            window.scrollBy({
                                top: scrollAmount,
                                behavior: 'smooth'
                            });
                        }
                    }, 100);
                }
            } else if (keyboardHeight > 0 && heightDiff < 50) { // Keyboard closed
                keyboardHeight = 0;
            }
        });

        // Add mobile-specific error handling
        document.addEventListener('error', function(e) {
            console.error('Mobile error:', e.error);
        });

        // Handle touch events for better mobile interaction
        document.addEventListener('touchstart', function(e) {
            // Add touch feedback for interactive elements
            if (e.target.classList.contains('step-buttom') ||
                e.target.classList.contains('info-input') ||
                e.target.classList.contains('from-input') ||
                e.target.classList.contains('to-input')) {
                e.target.style.transform = 'scale(0.98)';
            }
        });

        document.addEventListener('touchend', function(e) {
            // Remove touch feedback
            if (e.target.classList.contains('step-buttom') ||
                e.target.classList.contains('info-input') ||
                e.target.classList.contains('from-input') ||
                e.target.classList.contains('to-input')) {
                e.target.style.transform = '';
            }
        });
    }

    if (transportFrom && tooltipFrom) {
        transportFrom.addEventListener('focus', function() {
            tooltipFrom.classList.add('show');
        });
        transportFrom.addEventListener('blur', function() {
            tooltipFrom.classList.remove('show');
        });
    }

    if (transportTo && tooltipTo) {
        transportTo.addEventListener('focus', function() {
            tooltipTo.classList.add('show');
        });
        transportTo.addEventListener('blur', function() {
            tooltipTo.classList.remove('show');
        });
    }
});
</script>
<script async defer src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDPqmvF0Uf9aR1N1hQZVSUyibk__vkaegk&libraries=places&callback=initGoogleMaps" data-cmp-ab="2"></script>

<script>
function initializeAutocomplete(inputElement) {
    const autocompleteService = new google.maps.places.AutocompleteService();
    let currentSuggestion = '';
    let isComposing = false;

    function updateSuggestion() {
        const inputValue = inputElement.value;

        // Don't show suggestions if input is empty or if we're in the middle of composition
        if (inputValue.length === 0 || isComposing) {
            currentSuggestion = '';
            inputElement.dataset.userInputLength = inputValue.length;
            return;
        }

        // Don't update if cursor is not at the end of input
        if (inputElement.selectionStart !== inputValue.length || inputElement.selectionEnd !== inputValue.length) {
            return;
        }

        // Check if input is exactly 5 digits (zip code)
        const isZipCode = /^\d{5}$/.test(inputValue);

        autocompleteService.getPlacePredictions({
            input: inputValue,
            types: ['(regions)'],
            componentRestrictions: { country: 'us' }
        }, function(predictions, status) {
            // Make sure input hasn't changed while we were waiting for the API
            if (inputElement.value !== inputValue) {
                return;
            }

            if (status === google.maps.places.PlacesServiceStatus.OK && predictions && predictions.length > 0) {
                const firstPrediction = predictions[0].description;

                // If it's a 5-digit zip code, automatically select the first suggestion
                if (isZipCode) {
                    currentSuggestion = firstPrediction;
                    inputElement.value = firstPrediction;
                    inputElement.setSelectionRange(firstPrediction.length, firstPrediction.length);
                    inputElement.dataset.userInputLength = firstPrediction.length;
                    currentSuggestion = ''; // Clear suggestion since it's been accepted
                    return;
                }

                // Normal suggestion behavior for non-zip codes
                if (firstPrediction.toLowerCase().startsWith(inputValue.toLowerCase()) &&
                    firstPrediction.length > inputValue.length) {

                    currentSuggestion = firstPrediction;

                    // Set the full text
                    inputElement.value = firstPrediction;

                    // Select only the completion part (make it appear gray/highlighted)
                    inputElement.setSelectionRange(inputValue.length, firstPrediction.length);

                    // Store the original user input length
                    inputElement.dataset.userInputLength = inputValue.length;
                } else {
                    currentSuggestion = '';
                    inputElement.dataset.userInputLength = inputValue.length;
                }
            } else {
                currentSuggestion = '';
                inputElement.dataset.userInputLength = inputValue.length;
            }
        });
    }

    // Handle input events
    inputElement.addEventListener('input', function(e) {
        if (!isComposing) {
            // Always clear current suggestion when input changes
            currentSuggestion = '';

            // Update suggestion after a short delay
            setTimeout(() => {
                if (!isComposing && inputElement.value.length > 0) {
                    updateSuggestion();
                }
            }, 100);
        }
    });

    // Handle composition events (for international keyboards)
    inputElement.addEventListener('compositionstart', function() {
        isComposing = true;
    });

    inputElement.addEventListener('compositionend', function() {
        isComposing = false;
        updateSuggestion();
    });

    // Handle key events
    inputElement.addEventListener('keydown', function(e) {
        const userInputLength = parseInt(inputElement.dataset.userInputLength) || 0;
        const isAllSelected = inputElement.selectionStart === 0 && inputElement.selectionEnd === inputElement.value.length;

        if (e.key === 'Tab' && currentSuggestion && inputElement.selectionStart > userInputLength) {
            e.preventDefault();
            // Accept the suggestion
            inputElement.value = currentSuggestion;
            inputElement.setSelectionRange(currentSuggestion.length, currentSuggestion.length);
            currentSuggestion = '';
        } else if (e.key === 'Escape' && currentSuggestion) {
            // Reject the suggestion
            const userInput = inputElement.value.substring(0, userInputLength);
            inputElement.value = userInput;
            inputElement.setSelectionRange(userInput.length, userInput.length);
            currentSuggestion = '';
        } else if (e.key === 'ArrowRight' && inputElement.selectionStart === userInputLength && currentSuggestion) {
            // Accept suggestion with right arrow
            e.preventDefault();
            inputElement.value = currentSuggestion;
            inputElement.setSelectionRange(currentSuggestion.length, currentSuggestion.length);
            currentSuggestion = '';
        } else if (e.key === 'Backspace') {
            if (isAllSelected) {
                // Allow clearing all text
                e.preventDefault();
                inputElement.value = '';
                currentSuggestion = '';
                inputElement.dataset.userInputLength = '0';
            } else if (currentSuggestion && inputElement.selectionStart >= userInputLength && inputElement.selectionEnd > userInputLength) {
                // If there's a selection in the suggestion area, just remove the suggestion
                e.preventDefault();
                const userInput = inputElement.value.substring(0, userInputLength);
                inputElement.value = userInput;
                inputElement.setSelectionRange(userInput.length, userInput.length);
                currentSuggestion = '';
            } else {
                // Normal backspace - clear suggestion and let it proceed naturally
                currentSuggestion = '';
                // Don't prevent default - let the browser handle the backspace
            }
        } else if (e.key === 'Delete') {
            if (isAllSelected) {
                // Allow clearing all text
                e.preventDefault();
                inputElement.value = '';
                currentSuggestion = '';
                inputElement.dataset.userInputLength = '0';
            } else {
                // Clear suggestion when deleting
                currentSuggestion = '';
            }
        } else if (e.key.length === 1 && isAllSelected) {
            // User is typing over selected text - clear everything first
            e.preventDefault();
            inputElement.value = e.key;
            inputElement.setSelectionRange(1, 1);
            currentSuggestion = '';
            inputElement.dataset.userInputLength = '1';
            // Trigger suggestion update after a short delay
            setTimeout(() => updateSuggestion(), 10);
        }
    });

    // Handle blur - keep suggestion if user didn't explicitly reject it
    inputElement.addEventListener('blur', function() {
        setTimeout(() => {
            if (currentSuggestion && inputElement.value === currentSuggestion) {
                // Keep the full suggestion
                inputElement.setSelectionRange(currentSuggestion.length, currentSuggestion.length);
            }
            currentSuggestion = '';
        }, 150);
    });

    // Traditional autocomplete as fallback
    const autocomplete = new google.maps.places.Autocomplete(inputElement, {
        types: ['(regions)'],
        componentRestrictions: { country: 'us' }
    });

    autocomplete.addListener('place_changed', function () {
        const place = autocomplete.getPlace();
        if (place.address_components) {
            let city = '';
            let state = '';
            let zip = '';

            place.address_components.forEach(component => {
                const types = component.types;
                if (types.includes('locality') || types.includes('postal_town')) {
                    city = component.long_name;
                }
                if (types.includes('administrative_area_level_1')) {
                    state = component.short_name;
                }
                if (types.includes('postal_code')) {
                    zip = component.long_name;
                }
            });

            const formatted = `${city}, ${state} ${zip}, USA`;
            inputElement.value = formatted;
            inputElement.setSelectionRange(formatted.length, formatted.length);
            currentSuggestion = '';
        }
    });
}

// Google Maps API will be initialized via callback function
</script>
<script>
     // JavaScript to handle the multi-step form transitions
        // nextStep is now defined in the validation code below

        function goToStep(step) {
            document.querySelector('#step-' + (step - 1)).style.display = 'none';
            document.querySelector('#step-' + step).style.display = 'block';
        }
</script>



<script>
  // Initialize datepicker with custom styling
  $(function() {
    $("#available_date").datepicker({
      dateFormat: 'mm/dd/yy',
      minDate: 0, // Prevent selecting dates in the past
      showOtherMonths: true,
      selectOtherMonths: true,
      changeMonth: true,
      changeYear: true,
      yearRange: 'c:c+1', // Current year to next year
      beforeShow: function(input, inst) {
        // Add custom class to the datepicker
        setTimeout(function() {
          inst.dpDiv.addClass('custom-datepicker');
        }, 0);
      }
    });

    $("<style>")
      .prop("type", "text/css")
      .html(`
        .custom-datepicker {
          border: 1px solid #CDA565 !important;
          border-radius: 10px !important;
          font-family: 'Nohemi', sans-serif !important;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
        }
        .custom-datepicker .ui-datepicker-header {
          background: #b68544 !important;
          color: #ffffff !important;
          border: none !important;
          border-radius: 8px 8px 0 0 !important;
        }
        .custom-datepicker .ui-datepicker-calendar thead th {
          color: #46351A !important;
          font-weight: 600 !important;
        }
        .custom-datepicker .ui-state-default {
          background: #F3F1F5 !important;
          border: 1px solid #EAD4B9 !important;
          color: #46351A !important;
          text-align: center !important;
        }
        .custom-datepicker .ui-state-hover {
          background: #EBE2D8 !important;
          color: #222A31 !important;
        }
        .custom-datepicker .ui-state-active {
          background: #CDA565 !important;
          color: #ffffff !important;
          font-weight: bold !important;
        }
        .custom-datepicker .ui-datepicker-today .ui-state-default {
          border: 2px solid #b58544 !important;
        }
        .custom-datepicker .ui-datepicker-prev,
        .custom-datepicker .ui-datepicker-next {
          background: #b58544 !important;
          border: none !important;
          cursor: pointer !important;
        }
        .custom-datepicker .ui-datepicker-prev span,
        .custom-datepicker .ui-datepicker-next span {
          filter: brightness(0) invert(1) !important;
        }
      `)
      .appendTo("head");
  });

  // Function to validate fields and highlight empty ones
  function validateFields(fieldIds) {
    let allValid = true;

    fieldIds.forEach(function(fieldId) {
      const field = document.getElementById(fieldId);
      const isRadio = field.type === 'radio';

      if (isRadio) {
        const isChecked = document.querySelector(`input[name="${field.name}"]:checked`);
        if (!isChecked) {
          // Highlight radio group container
          document.querySelector(`input[name="${field.name}"]`).closest(".rowtransporttyp").classList.add("radio-error");
          allValid = false;
        } else {
          document.querySelector(`input[name="${field.name}"]`).closest(".rowtransporttyp").classList.remove("radio-error");
        }
      } else {
        // For regular inputs
        if (field.value.trim() === '') {
          // Add error class to highlight the field
          field.closest(".input-wrapper").classList.add("input-error");
          allValid = false;
        } else {
          field.closest(".input-wrapper").classList.remove("input-error");
        }
      }
    });

    return allValid;
  }

  // Add validation to the date field specifically
  document.getElementById('available_date').addEventListener('change', function() {
    if (this.value.trim() === '') {
      this.closest(".input-wrapper").classList.add("input-error");
    } else {
      this.closest(".input-wrapper").classList.remove("input-error");
    }
  });

  // Validate before moving to next step
  function nextStep(step) {
    // If moving to step 3, validate step 2 fields
    if (step === 3) {
      const step2Fields = ['vehicle_year', 'vehicle_brand', 'vehicle_model', 'vehicle_operable'];
      if (!validateFields(step2Fields)) {
        alert('Please fill out all the required fields.');
        return;
      }
    }

    // If moving to step 2, validate step 1 fields
    if (step === 2) {
      const step1Fields = ['transport_from', 'transport_to', 'transport_type'];
      if (!validateFields(step1Fields)) {
        alert(' Please fill out all the required fields.');
        return;
      }
    }

    document.querySelector('#step-' + (step - 1)).style.display = 'none';
    document.querySelector('#step-' + step).style.display = 'block';
  }

  // Override the original nextStep function
  window.nextStep = nextStep;

  // Form submission validation
  document.getElementById('transportForm').addEventListener('submit', function(event) {
    // Prevent the default form submission first
    event.preventDefault();

    // Check specifically for the date field
    const dateField = document.getElementById('available_date');
    if (!dateField.value.trim()) {
      dateField.closest(".input-wrapper").classList.add("input-error");
      alert('Please select an available date.');
      return false;
    }

    // Check all other required fields
    const requiredFields = [
        'transport_from', 'transport_to', 'transport_type',
        'vehicle_year', 'vehicle_brand', 'vehicle_model',
        'vehicle_operable', 'email', 'available_date', 'name', 'phone'
    ];

    if (!validateFields(requiredFields)) {
      alert('Please fill out all the required fields.');
      return false;
    } else {
      // If all validations pass, submit the form
      alert('Your form has been successfully submitted!');
      this.submit();
    }
  });

  // Also add a click handler to the submit button as a backup
  document.getElementById('finishButton').addEventListener('click', function(event) {
    // The form submit event handler above will take care of validation
    // This is just a backup in case the form submit event doesn't fire
    const dateField = document.getElementById('available_date');
    if (!dateField.value.trim()) {
      dateField.closest(".input-wrapper").classList.add("input-error");
      alert('Please select an available date.');
      event.preventDefault();
    }
  });

</script>


<script type="text/javascript">
    document.addEventListener("DOMContentLoaded", function () {
        setTimeout(function () {
            let submitButton = document.getElementById("finishButton");
            let transportForm = document.getElementById("transportForm");

            if (submitButton && transportForm) {
                console.log("iubenda initialized:", submitButton, transportForm);

                _iub.cons_instructions.push(["load", {
                    submitElement: submitButton,
                    form: {
                    selector: transportForm,
                                map: {
                                    subject: {
                                        full_name: "name",
                                        // first_name: "name",
                                        email: "email",
                                        phones: "phone"
                                    },
                                    preferences: {
                                        transport_from: "transport_from",
                                        transport_to: "transport_to",
                                        transport_type: "transport_type",
                                        vehicle_year: "vehicle_year",
                                        vehicle_brand: "vehicle_brand",
                                        vehicle_model: "vehicle_model",
                                        available_date: "available_date"
                                    }
                                }
                            },
                            consent: {
                                legal_notices: [
                                    { identifier: "privacy_policy" },
                                    { identifier: "cookie_policy" },
                                    { identifier: "terms" }
                                ]
                            }
                        }]);
                    } else {
                        console.error("Error: Submit button or form not found.");
                    }
                }, 2000);
            });
</script>