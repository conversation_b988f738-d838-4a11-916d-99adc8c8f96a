<div class="slider-pad container-xs">
<div class="fix-slider-slider-container">
  <div class="fix-slider-text-section">
    <div class="fix-slider-main-title">
      Shipping your car with Safe Car Hauler in 3 easy steps!
    </div>

    <div class="fix-slider-steps">
      <button class="fix-slider-step-button active" onclick="showContent(1)">Step 1</button>
      <button class="fix-slider-step-button" onclick="showContent(2)">Step 2</button>
      <button class="fix-slider-step-button" onclick="showContent(3)">Step 3</button>
    </div>

    <div class="fix-slider-content active" id="content-1">
      <div class="fix-slider-title">Get Your Car Shipping Quote in Under a Minute</div>
      <div class="description-11 ui text size-textmd">
        Request a quick quote online or call
        <a href="tel:8778788008" class="getaninstant-span-call2">(*************</a>.
        Choose your transport option and book by phone or email. Once confirmed, we match your shipment with a fully insured, trusted carrier from our network.
      </div>
    </div>

    <div class="fix-slider-content" id="content-2">
      <div class="fix-slider-title">Your Vehicle Is Picked Up</div>
      <div class="description-11 ui text size-textmd">
        We pick up your vehicle from your chosen location and perform a thorough inspection, documented on the Bill of Lading, to ensure everything is in order.
      </div>
    </div>

    <div class="fix-slider-content" id="content-3">
      <div class="fix-slider-title">Your Vehicle Arrives Safely</div>
      <div class="description-11 ui text size-textmd">
        We keep you updated during transit. You’ll get a heads-up on the delivery time, and we’ll conduct a final inspection at drop-off to confirm your vehicle arrives in perfect condition.
      </div>
    </div>
  </div>

  <div class="fix-slider-image-section">
    <img src="../public/images/Book2.png" id="step-image" alt="Step Image">
  </div>
</div>
</div>
<script>
  function showContent(step) {
    document.querySelectorAll('.fix-slider-step-button').forEach((button, index) => {
      button.classList.toggle('active', index + 1 === step);
    });

    document.querySelectorAll('.fix-slider-content').forEach((content, index) => {
      content.classList.toggle('active', index + 1 === step);
    });

    const image = document.getElementById('step-image');
    if (step === 1) {
      image.src = "../public/images/Book2.png";
    } else if (step === 2) {
      image.src = "../public/images/Truck1.png";
    } else if (step === 3) {
      image.src = "../public/images/CarKey1.png";
    }
  }
</script>